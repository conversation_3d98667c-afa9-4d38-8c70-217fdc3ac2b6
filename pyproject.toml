[project]
name = "duckdb-query"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "duckdb>=1.2.2",
    "pytz>=2025.2",
    "discord.py>=2.3.2",
    "python-dotenv>=1.0.0",
    "pandas>=2.1.0",
    "matplotlib>=3.8.0",
    "flask>=2.0.0",
    "gunicorn>=20.1.0",
    "flask-compress>=1.17",
    "tabulate>=0.9.0",
    "boto3>=1.38.29",
    "google-genai>=1.18.0",
    "docker>=7.1.0",
]

[dependency-groups]
dev = [
    "ruff>=0.11.10",
]
