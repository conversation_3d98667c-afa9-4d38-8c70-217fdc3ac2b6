import os

import docker
import duckdb
from duckdb.duckdb import DuckDBPyConnection
from google import genai
from google.genai.types import GenerateContentConfig

IGNORED_VIEWS = [
    "active_matches",
    "player_card",
    "match_salts",
    "match_player_item_v2",
    "match_player_encoded_items",
    "match_player_account_id",
]


def get_duckdb_views_info(
    con: DuckDBPyConnection, ignore_views: list[str] | None = None
):
    views = con.execute(
        "SELECT view_name FROM duckdb_views() WHERE schema_name = 'main' AND internal is FALSE ORDER BY view_name"
    ).fetchall()
    output_lines = [
        "# DuckDB Views Schema Description",
        "# --- START OF VIEWS ---",
    ]
    for (view,) in views:
        if ignore_views and view in ignore_views:
            continue
        schema = con.execute(f"DESCRIBE {view}").fetchall()
        output_lines.extend(
            [
                "",
                "# --- View Details ---",
                f"# View Name: {view}",
                "# View Schema:",
            ]
            + [f"#   {col[0]}: {col[1]}" for col in schema]
        )

    output_lines.append("\n# --- END OF VIEWS ---")
    return "\n".join(output_lines)


if __name__ == "__main__":
    with duckdb.connect(database="duckdb", read_only=True) as con:
        views_schema = get_duckdb_views_info(con, IGNORED_VIEWS)
    system_prompt = f"""
You are a Python and DuckDB expert. Your task is to write a python script to answer the user question.

To query the DuckDB database, use the following views:
{views_schema}

The DuckDB database is located at /data/duckdb
If you call it from python you can query it like this:
```python
import duckdb
with duckdb.connect(database="/data/duckdb", read_only=True) as con:
    ...
```

First give a detailed plan what you are trying to do, then write the python script enclosed in triple backticks like:
```python
...
```
Available packages are: duckdb, numpy, pandas
    """
    question = input("Question: ")
    client = genai.Client(api_key="AIzaSyCENJgtajwaRPjjgFxTkoKXTWLtxi5Btvs")
    response_stream = client.models.generate_content_stream(
        model="gemini-2.5-flash-preview-05-20",
        config=GenerateContentConfig(system_instruction=system_prompt),
        contents=question,
    )
    explanation = ""
    python_script = ""
    is_python = False
    for response in response_stream:
        if not is_python and "```python" in response.text:
            is_python = True
            parts = response.text.split("```python")
            explanation += parts[0]
            python_script += parts[1]
            continue
        if not is_python and "```" in response.text:
            is_python = True
            parts = response.text.split("```")
            explanation += parts[0]
            python_script += parts[1]
            continue
        if is_python:
            python_script += response.text
        else:
            explanation += response.text
    python_script = python_script.replace("```", "").strip()

    with open("llm_output.py", "w") as f:
        f.write("".join(python_script))

    # Start a Docker container with the duckdb database mounted
    client = docker.from_env()
    container = client.containers.run(
        "python:3.13",
        command="/bin/bash",
        volumes={
            f"{os.getcwd()}/duckdb": {"bind": "/data/duckdb"},
            f"{os.getcwd()}/llm_output.py": {"bind": "/data/llm_output.py"},
        },
        detach=True,
        tty=True,  # Allocate a pseudo-TTY to keep the container running
        stdin_open=True,  # Keep STDIN open
    )
    # Wait for the container to be fully up before running commands
    container.reload()

    # Setup the container
    container.exec_run("apt-get update")
    container.exec_run("apt-get install -y curl")
    container.exec_run("pip install duckdb numpy pandas --upgrade")

    # Execute the python script
    print("\n--------------------")
    print(explanation.strip())
    exit_code, output = container.exec_run("python /data/llm_output.py")
    print(output.decode("utf-8"))

    # Stop the container
    container.stop()
    container.remove()

    breakpoint()
